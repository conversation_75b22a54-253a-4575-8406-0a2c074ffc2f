"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Ban,
  AlertTriangle,
  Info,
  CheckCircle,
} from "lucide-react";
import { formatLayBuyPrice, calculateRefundAmount } from "@/lib/lay-buy-utils";

interface LayBuyCancelRequestProps {
  orderId: string;
  orderNumber: string;
  amountPaid: number;
  dueDate: string;
  onCancelRequested: () => void;
}

export default function LayBuyCancelRequest({
  orderId,
  orderNumber,
  amountPaid,
  dueDate,
  onCancelRequested,
}: LayBuyCancelRequestProps) {
  const [showDialog, setShowDialog] = useState(false);
  const [reason, setReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  const dueDateObj = new Date(dueDate);
  const refundInfo = calculateRefundAmount(amountPaid, dueDateObj);

  const handleSubmitCancelRequest = async () => {
    setIsSubmitting(true);
    try {
      // For now, we'll just show a success message
      // In a real implementation, you might want to create a cancellation request
      // that admins can review and approve
      
      setSuccess(true);
      setShowDialog(false);
      setReason("");
      onCancelRequested();
      
      // Hide success message after 5 seconds
      setTimeout(() => setSuccess(false), 5000);
    } catch (error) {
      console.error("Error submitting cancel request:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <Alert className="border-green-200 bg-green-50">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          Cancellation request submitted successfully! Our team will review your request and contact you within 24 hours.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Ban className="h-5 w-5" />
            Cancel Order
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              You can cancel your Lay-Buy order at any time. Refund amounts depend on when you cancel.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-900 mb-2">Refund Information</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <div className="flex justify-between">
                  <span>Amount Paid:</span>
                  <span className="font-medium">{formatLayBuyPrice(amountPaid)}</span>
                </div>
                {refundInfo.isEligible ? (
                  <div className="flex justify-between">
                    <span>Refund Amount (50%):</span>
                    <span className="font-medium text-green-600">{formatLayBuyPrice(refundInfo.refundAmount)}</span>
                  </div>
                ) : (
                  <div className="text-red-600 font-medium">
                    No refund available (past due date)
                  </div>
                )}
              </div>
            </div>

            {refundInfo.isEligible ? (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Early Cancellation Policy:</strong> If you cancel before the 6-week deadline, 
                  you'll receive a 50% refund. The remaining 50% covers holding and administrative costs.
                </AlertDescription>
              </Alert>
            ) : (
              <Alert className="border-red-200 bg-red-50">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  <strong>Late Cancellation:</strong> Your payment deadline has passed. 
                  Cancellations after the due date are not eligible for refunds.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <Button 
            onClick={() => setShowDialog(true)}
            variant="outline"
            className="w-full border-red-300 text-red-700 hover:bg-red-50"
          >
            <Ban className="mr-2 h-4 w-4" />
            Request Cancellation
          </Button>
        </CardContent>
      </Card>

      {/* Cancellation Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Ban className="h-5 w-5 text-red-600" />
              Cancel Lay-Buy Order
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel order {orderNumber}?
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason for Cancellation (Optional)
              </label>
              <Textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Please let us know why you're cancelling this order..."
                rows={3}
              />
            </div>

            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">Cancellation Summary</h4>
              <div className="text-sm space-y-1">
                <div className="flex justify-between">
                  <span>Order Number:</span>
                  <span className="font-medium">{orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span>Amount Paid:</span>
                  <span className="font-medium">{formatLayBuyPrice(amountPaid)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Refund Amount:</span>
                  <span className={`font-medium ${refundInfo.isEligible ? 'text-green-600' : 'text-red-600'}`}>
                    {refundInfo.isEligible ? formatLayBuyPrice(refundInfo.refundAmount) : 'M0.00'}
                  </span>
                </div>
              </div>
            </div>

            {refundInfo.isEligible && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Your refund will be processed within 3-5 business days after approval.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDialog(false)}>
              Keep Order
            </Button>
            <Button
              onClick={handleSubmitCancelRequest}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Submitting...
                </>
              ) : (
                <>
                  <Ban className="h-4 w-4 mr-2" />
                  Submit Cancellation
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
